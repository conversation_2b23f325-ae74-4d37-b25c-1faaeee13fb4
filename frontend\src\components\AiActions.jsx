import { useState } from "react";
import api from "../api";

export default function AiActions({ note }) {
  const [result, setResult] = useState("");
  const [loading, setLoading] = useState("");
  const [showResult, setShowResult] = useState(false);

  const callAI = async (type) => {
    if (loading) return;

    setLoading(type);
    setResult("");

    try {
      const res = await api.post(`/ai/${type}`, { content: note.content });
      setResult(res.data.result || res.data.summary || res.data.keywords || res.data.rewritten || JSON.stringify(res.data));
      setShowResult(true);
    } catch (error) {
      setResult("Error: " + (error.response?.data?.message || "AI request failed"));
      setShowResult(true);
    } finally {
      setLoading("");
    }
  };

  const aiActions = [
    {
      type: "summarize",
      label: "Summarize",
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      gradient: "from-info to-info-light",
      description: "Get a concise summary"
    },
    {
      type: "keywords",
      label: "Keywords",
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
        </svg>
      ),
      gradient: "from-secondary to-secondary-light",
      description: "Extract key terms"
    },
    {
      type: "rewrite",
      label: "Rewrite",
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
        </svg>
      ),
      gradient: "from-success to-success-light",
      description: "Improve the text"
    }
  ];

  return (
    <div className="space-y-3">
      {/* AI Action Buttons */}
      <div className="flex flex-wrap gap-2">
        {aiActions.map((action) => (
          <button
            key={action.type}
            onClick={() => callAI(action.type)}
            disabled={loading === action.type}
            className={`btn btn-sm group relative overflow-hidden bg-gradient-to-r ${action.gradient} text-white shadow-md hover:shadow-lg transition-all duration-200`}
            title={action.description}
          >
            <span className="flex items-center gap-2 relative z-10">
              {loading === action.type ? (
                <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              ) : (
                action.icon
              )}
              <span className="font-medium">
                {loading === action.type ? "Processing..." : action.label}
              </span>
            </span>
          </button>
        ))}
      </div>

      {/* AI Result Display */}
      {showResult && result && (
        <div className="glass-strong rounded-xl overflow-hidden animate-scale-in">
          <div className="flex items-center justify-between p-3 border-b border-glass-border">
            <div className="flex items-center gap-2">
              <div className="w-6 h-6 bg-gradient-to-br from-accent to-warning rounded-lg flex items-center justify-center">
                <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <span className="text-sm font-semibold text-text-primary">AI Result</span>
            </div>
            <button
              onClick={() => setShowResult(false)}
              className="p-1 text-text-tertiary hover:text-text-primary transition-colors rounded"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <div className="p-4">
            <div className="text-sm text-text-secondary leading-relaxed whitespace-pre-wrap">
              {result}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
