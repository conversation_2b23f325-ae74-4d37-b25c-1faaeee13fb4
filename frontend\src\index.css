@import './theme.css';

/* Enhanced Reset and base styles */
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  margin: 0;
  font-family: var(--font-family-sans);
  font-weight: 400;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  background: var(--bg-primary);
  background-image: var(--gradient-mesh);
  background-attachment: fixed;
  background-size: 100% 100%;
  min-height: 100vh;
  color: var(--text-primary);
  overflow-x: hidden;
}

/* Enhanced body background with animated gradient */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-primary);
  opacity: 0.03;
  z-index: -1;
  animation: pulse 8s ease-in-out infinite alternate;
}

code {
  font-family: var(--font-family-mono);
  font-size: 0.9em;
  background: var(--glass-bg);
  padding: 0.2em 0.4em;
  border-radius: var(--radius-sm);
  border: 1px solid var(--glass-border);
}

pre {
  font-family: var(--font-family-mono);
  background: var(--glass-bg);
  padding: var(--space-md);
  border-radius: var(--radius-lg);
  border: 1px solid var(--glass-border);
  overflow-x: auto;
  line-height: 1.5;
}

/* Enhanced utility classes for line clamping */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-5 {
  display: -webkit-box;
  -webkit-line-clamp: 5;
  line-clamp: 5;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Enhanced scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: var(--radius-full);
  transition: all var(--duration-normal) ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--primary-light), var(--secondary-light));
}

::-webkit-scrollbar-corner {
  background: var(--bg-tertiary);
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--primary) var(--bg-tertiary);
}

/* Selection styling */
::selection {
  background: rgba(139, 92, 246, 0.3);
  color: var(--text-primary);
}

::-moz-selection {
  background: rgba(139, 92, 246, 0.3);
  color: var(--text-primary);
}

/* Focus styles */
:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* Link styles */
a {
  color: var(--primary-light);
  text-decoration: none;
  transition: color var(--duration-fast) ease;
}

a:hover {
  color: var(--primary);
  text-decoration: underline;
}

/* Image optimization */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Form elements reset */
button,
input,
textarea,
select {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

/* Accessibility improvements */
.visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}