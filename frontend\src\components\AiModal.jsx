import { useEffect, useMemo, useState } from "react";

export default function AiModal({ open, onClose, answerText, quiz }) {
  const [userAnswers, setUserAnswers] = useState([]);
  const [submitted, setSubmitted] = useState(false);
  const [showExplanations, setShowExplanations] = useState(false);
  const total = quiz?.questions?.length || 0;

  useEffect(() => {
    if (open) {
      setSubmitted(false);
      setUserAnswers(Array(total).fill(null));
      setShowExplanations(false);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    } else {
      // Restore body scroll when modal is closed
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [open, total]);

  const score = useMemo(() => {
    if (!submitted || !quiz) return 0;
    return quiz.questions.reduce((acc, q, i) => acc + (userAnswers[i] === q.correctIndex ? 1 : 0), 0);
  }, [submitted, quiz, userAnswers]);

  const scorePercentage = useMemo(() => {
    if (total === 0) return 0;
    return Math.round((score / total) * 100);
  }, [score, total]);

  const getScoreColor = () => {
    if (scorePercentage >= 80) return 'text-success';
    if (scorePercentage >= 60) return 'text-warning';
    return 'text-error';
  };

  const getScoreMessage = () => {
    if (scorePercentage >= 90) return 'Excellent! 🎉';
    if (scorePercentage >= 80) return 'Great job! 👏';
    if (scorePercentage >= 70) return 'Good work! 👍';
    if (scorePercentage >= 60) return 'Not bad! 📚';
    return 'Keep studying! 💪';
  };

  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 animate-fade-in">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/80 backdrop-blur-md"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="relative max-h-[90vh] w-full max-w-4xl glass-stronger rounded-3xl overflow-hidden shadow-2xl border border-glass-border-strong animate-scale-in">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-glass-border">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-accent to-warning rounded-xl flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <div>
              <h3 className="text-2xl font-bold text-gradient">AI Assistant</h3>
              <p className="text-text-tertiary text-sm">
                {quiz ? `Quiz • ${total} questions` : 'Analysis Result'}
              </p>
            </div>
          </div>

          <button
            onClick={onClose}
            className="btn btn-ghost btn-sm group"
            title="Close modal"
          >
            <svg className="w-5 h-5 transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="overflow-auto max-h-[calc(90vh-140px)] p-6">
          {/* Answer Text */}
          {answerText && (
            <div className="mb-8">
              <div className="flex items-center gap-2 mb-4">
                <div className="w-6 h-6 bg-gradient-to-br from-info to-info-light rounded-lg flex items-center justify-center">
                  <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h4 className="text-lg font-semibold text-text-primary">Analysis Result</h4>
              </div>
              <div className="glass p-6 rounded-2xl">
                <pre className="whitespace-pre-wrap text-text-primary font-sans leading-relaxed text-sm">
                  {answerText}
                </pre>
              </div>
            </div>
          )}

          {/* Quiz */}
          {quiz && (
            <div>
              {/* Quiz Header */}
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-2">
                  <div className="w-6 h-6 bg-gradient-to-br from-accent to-warning rounded-lg flex items-center justify-center">
                    <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h4 className="text-lg font-semibold text-text-primary">Interactive Quiz</h4>
                </div>

                {submitted && (
                  <div className="text-right">
                    <div className={`text-2xl font-bold ${getScoreColor()}`}>
                      {score}/{total}
                    </div>
                    <div className="text-text-tertiary text-sm">
                      {scorePercentage}% • {getScoreMessage()}
                    </div>
                  </div>
                )}
              </div>

              {/* Questions */}
              <div className="space-y-6">
                {quiz.questions?.map((q, qi) => (
                  <div key={qi} className="glass p-6 rounded-2xl">
                    <div className="flex items-start gap-3 mb-4">
                      <div className="w-8 h-8 bg-gradient-to-br from-primary to-secondary rounded-xl flex items-center justify-center flex-shrink-0 mt-1">
                        <span className="text-white font-bold text-sm">{qi + 1}</span>
                      </div>
                      <div className="flex-1">
                        <h5 className="font-semibold text-text-primary leading-relaxed">
                          {q.question}
                        </h5>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 gap-3 ml-11">
                      {q.options.map((opt, oi) => {
                        const isCorrect = submitted && oi === q.correctIndex;
                        const isSelected = userAnswers[qi] === oi;
                        const isWrongSelection = submitted && isSelected && oi !== q.correctIndex;

                        return (
                          <button
                            key={oi}
                            type="button"
                            onClick={() => !submitted && setUserAnswers((prev) => prev.map((v, idx) => (idx === qi ? oi : v)))}
                            disabled={submitted}
                            className={`text-left p-4 rounded-xl border transition-all duration-200 ${
                              submitted
                                ? isCorrect
                                  ? "bg-success/10 border-success text-success-light"
                                  : isWrongSelection
                                  ? "bg-error/10 border-error text-error-light"
                                  : "bg-glass-bg border-glass-border text-text-secondary"
                                : isSelected
                                ? "bg-primary/10 border-primary text-primary-light"
                                : "bg-glass-bg border-glass-border text-text-primary hover:bg-glass-bg-strong hover:border-glass-border-strong"
                            }`}
                          >
                            <div className="flex items-center gap-3">
                              <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                                submitted
                                  ? isCorrect
                                    ? "border-success bg-success"
                                    : isWrongSelection
                                    ? "border-error bg-error"
                                    : "border-glass-border"
                                  : isSelected
                                  ? "border-primary bg-primary"
                                  : "border-glass-border"
                              }`}>
                                {(isSelected || (submitted && isCorrect)) && (
                                  <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                                  </svg>
                                )}
                              </div>
                              <span className="font-medium">{opt}</span>
                            </div>
                          </button>
                        );
                      })}
                    </div>

                    {submitted && showExplanations && q.explanation && (
                      <div className="mt-4 ml-11 p-4 bg-info/10 border border-info/20 rounded-xl">
                        <div className="flex items-start gap-2">
                          <svg className="w-4 h-4 text-info mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <div>
                            <div className="text-info font-medium text-sm mb-1">Explanation</div>
                            <div className="text-text-secondary text-sm leading-relaxed">
                              {q.explanation}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* Quiz Actions */}
              <div className="flex items-center justify-between mt-8 pt-6 border-t border-glass-border">
                <div className="flex items-center gap-3">
                  {submitted && quiz.questions.some(q => q.explanation) && (
                    <button
                      onClick={() => setShowExplanations(!showExplanations)}
                      className="btn btn-secondary btn-sm"
                    >
                      <span className="flex items-center gap-2">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        {showExplanations ? 'Hide' : 'Show'} Explanations
                      </span>
                    </button>
                  )}
                </div>

                <div className="flex items-center gap-3">
                  {!submitted && (
                    <button
                      onClick={() => setSubmitted(true)}
                      disabled={userAnswers.some(answer => answer === null)}
                      className="btn btn-primary"
                    >
                      <span className="flex items-center gap-2">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Submit Quiz
                      </span>
                    </button>
                  )}

                  {submitted && (
                    <button
                      onClick={() => {
                        setSubmitted(false);
                        setUserAnswers(Array(total).fill(null));
                        setShowExplanations(false);
                      }}
                      className="btn btn-secondary"
                    >
                      <span className="flex items-center gap-2">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        Retry Quiz
                      </span>
                    </button>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}


