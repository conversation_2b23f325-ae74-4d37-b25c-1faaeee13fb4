import { useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import api from "../api";
import AiModal from "../components/AiModal";
import Navbar from "../components/Navbar";

export default function UploadPDF() {
  const [file, setFile] = useState(null);
  const [parsedText, setParsedText] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [aiResult, setAiResult] = useState("");
  const [quiz, setQuiz] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [aiLoading, setAiLoading] = useState("");
  const fileInputRef = useRef(null);
  const navigate = useNavigate();

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    if (selectedFile && selectedFile.type === "application/pdf") {
      setFile(selectedFile);
      setError("");
    } else {
      setError("Please select a valid PDF file");
    }
  };

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const droppedFile = e.dataTransfer.files[0];
    if (droppedFile && droppedFile.type === "application/pdf") {
      setFile(droppedFile);
      setError("");
    } else {
      setError("Please drop a valid PDF file");
    }
  };

  const doUpload = async () => {
    if (!file) {
      fileInputRef.current?.click();
      return;
    }

    setLoading(true);
    setError("");
    setParsedText("");

    try {
      const formData = new FormData();
      formData.append("pdf", file);
      const res = await api.post("/pdf/upload", formData);
      const text = res.data.text || "";
      setParsedText(text);
      if (!text.trim()) {
        setError("No extractable text found. This PDF might be scanned or image-only.");
      }
    } catch (err) {
      setError(err.response?.data?.message || "Upload failed");
    } finally {
      setLoading(false);
    }
  };

  const handleAI = async (type) => {
    if (aiLoading) return;

    setAiLoading(type);
    try {
      const res = await api.post(`/ai/${type}`, { content: parsedText });
      setAiResult(res.data.result || res.data.summary || res.data.keywords || res.data.rewritten);
      setQuiz(null);
      setIsModalOpen(true);
    } catch (err) {
      setAiResult(err.response?.data?.message || "AI request failed");
      setIsModalOpen(true);
    } finally {
      setAiLoading("");
    }
  };

  const buildQuiz = async (n = 5) => {
    if (aiLoading) return;

    setAiLoading("quiz");
    try {
      const res = await api.post(`/ai/quiz`, { content: parsedText, numQuestions: n });
      const serverQuiz = res.data.quiz || null;
      if (serverQuiz) {
        setQuiz(serverQuiz);
      } else if (res.data.quizText) {
        const txt = res.data.quizText;
        const tryParse = (raw) => { try { const q = JSON.parse(raw); if (q && Array.isArray(q.questions)) return q; } catch {} return null; };
        let q = tryParse(txt);
        if (!q) {
          const m = txt.match(/```(?:json)?\n([\s\S]*?)\n```/i);
          if (m) q = tryParse(m[1]);
        }
        if (!q) {
          const first = txt.indexOf('{');
          const last = txt.lastIndexOf('}');
          if (first !== -1 && last !== -1 && last > first) q = tryParse(txt.slice(first, last + 1));
        }
        if (q) setQuiz(q); else setAiResult(txt);
      }
      setIsModalOpen(true);
    } catch (err) {
      setAiResult(err.response?.data?.message || "AI request failed");
      setIsModalOpen(true);
    } finally {
      setAiLoading("");
    }
  };

  const aiActions = [
    {
      type: "summarize",
      label: "Summarize",
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      gradient: "from-info to-info-light",
      description: "Get a concise summary of the PDF content"
    },
    {
      type: "rewrite",
      label: "Rewrite",
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
        </svg>
      ),
      gradient: "from-success to-success-light",
      description: "Improve and restructure the text"
    },
    {
      type: "keywords",
      label: "Keywords",
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
        </svg>
      ),
      gradient: "from-secondary to-secondary-light",
      description: "Extract key terms and concepts"
    }
  ];

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-20 w-72 h-72 bg-gradient-to-br from-accent/20 to-primary/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-gradient-to-tr from-primary/20 to-secondary/20 rounded-full blur-3xl animate-pulse" style={{animationDelay: '3s'}}></div>
      </div>

      <Navbar />

      <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-12 animate-fade-in">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-accent to-primary rounded-3xl mb-6 shadow-glow">
            <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
          </div>
          <h1 className="text-5xl font-extrabold text-gradient mb-4 tracking-tight">Upload PDF</h1>
          <p className="text-text-secondary text-xl font-medium max-w-2xl mx-auto">
            Extract text from your PDF documents and enhance them with AI-powered analysis
          </p>
        </div>

        {/* Upload Section */}
        <div className="glass-strong p-8 rounded-3xl mb-8 animate-scale-in" style={{animationDelay: '0.2s'}}>
          <div className="flex items-center gap-3 mb-6">
            <div className="w-10 h-10 bg-gradient-to-br from-accent to-primary rounded-xl flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-gradient">Upload Your PDF</h2>
          </div>

          {/* File Drop Zone */}
          <div
            className={`relative border-2 border-dashed rounded-2xl p-8 text-center transition-all duration-300 ${
              dragActive
                ? 'border-primary bg-primary/5 scale-105'
                : file
                  ? 'border-success bg-success/5'
                  : 'border-glass-border hover:border-glass-border-strong hover:bg-glass-bg'
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept="application/pdf"
              onChange={handleFileChange}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            />

            <div className="space-y-4">
              {file ? (
                <div className="flex items-center justify-center gap-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-success to-success-light rounded-xl flex items-center justify-center">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div className="text-left">
                    <p className="text-text-primary font-semibold">{file.name}</p>
                    <p className="text-text-tertiary text-sm">{(file.size / 1024 / 1024).toFixed(2)} MB</p>
                  </div>
                </div>
              ) : (
                <>
                  <div className="w-16 h-16 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-2xl mx-auto flex items-center justify-center">
                    <svg className="w-8 h-8 text-text-tertiary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-text-primary font-semibold mb-2">
                      {dragActive ? "Drop your PDF here" : "Drag & drop your PDF here"}
                    </p>
                    <p className="text-text-tertiary text-sm">
                      or <span className="text-primary font-medium cursor-pointer hover:underline">browse files</span>
                    </p>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="mt-4 p-4 bg-error/10 border border-error/20 rounded-xl text-error-light text-sm font-medium animate-fade-in">
              <div className="flex items-center gap-2">
                <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {error}
              </div>
            </div>
          )}

          {/* Upload Button */}
          <div className="mt-6">
            <button
              onClick={doUpload}
              disabled={loading || !file}
              className="btn btn-primary btn-lg w-full group"
            >
              <span className="flex items-center justify-center gap-2">
                {loading ? (
                  <svg className="w-5 h-5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5 transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                )}
                {loading ? "Processing PDF..." : file ? "Extract Text from PDF" : "Choose a PDF File"}
              </span>
            </button>
          </div>
        </div>

        {/* Extracted Text and AI Actions */}
        {parsedText && (
          <div className="glass-strong p-8 rounded-3xl animate-scale-in">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-success to-success-light rounded-xl flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-gradient">Extracted Text</h2>
            </div>

            {/* Text Display */}
            <div className="glass p-4 rounded-xl mb-6">
              <textarea
                className="w-full h-64 bg-transparent text-text-primary resize-none focus:outline-none font-mono text-sm leading-relaxed"
                value={parsedText}
                readOnly
                placeholder="Extracted text will appear here..."
              />
            </div>

            {/* AI Actions */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-text-primary mb-4">AI Analysis Options</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {aiActions.map((action) => (
                  <button
                    key={action.type}
                    onClick={() => handleAI(action.type)}
                    disabled={aiLoading === action.type}
                    className={`btn group relative overflow-hidden bg-gradient-to-r ${action.gradient} text-white shadow-md hover:shadow-lg transition-all duration-200 p-4 h-auto flex-col`}
                    title={action.description}
                  >
                    <span className="flex flex-col items-center gap-3 relative z-10">
                      {aiLoading === action.type ? (
                        <svg className="w-6 h-6 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                      ) : (
                        action.icon
                      )}
                      <div className="text-center">
                        <div className="font-semibold">
                          {aiLoading === action.type ? "Processing..." : action.label}
                        </div>
                        <div className="text-xs opacity-90 mt-1">
                          {action.description}
                        </div>
                      </div>
                    </span>
                  </button>
                ))}

                {/* Quiz Button */}
                <button
                  onClick={() => buildQuiz(5)}
                  disabled={aiLoading === "quiz"}
                  className="btn group relative overflow-hidden bg-gradient-to-r from-accent to-warning text-white shadow-md hover:shadow-lg transition-all duration-200 p-4 h-auto flex-col"
                  title="Generate a quiz from the PDF content"
                >
                  <span className="flex flex-col items-center gap-3 relative z-10">
                    {aiLoading === "quiz" ? (
                      <svg className="w-6 h-6 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                    ) : (
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    )}
                    <div className="text-center">
                      <div className="font-semibold">
                        {aiLoading === "quiz" ? "Generating..." : "Generate Quiz"}
                      </div>
                      <div className="text-xs opacity-90 mt-1">
                        Create interactive questions
                      </div>
                    </div>
                  </span>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* AI Modal */}
        <AiModal
          open={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          answerText={aiResult}
          quiz={quiz}
        />
      </div>
    </div>
  );
}
