@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600&display=swap');

:root {
  /* Enhanced Color Palette */
  --primary: #8b5cf6;
  --primary-light: #a78bfa;
  --primary-dark: #7c3aed;
  --primary-darker: #6d28d9;

  --secondary: #06b6d4;
  --secondary-light: #22d3ee;
  --secondary-dark: #0891b2;

  --accent: #f59e0b;
  --accent-light: #fbbf24;
  --accent-dark: #d97706;

  --success: #10b981;
  --success-light: #34d399;
  --success-dark: #059669;

  --warning: #f59e0b;
  --warning-light: #fbbf24;
  --warning-dark: #d97706;

  --error: #ef4444;
  --error-light: #f87171;
  --error-dark: #dc2626;

  --info: #3b82f6;
  --info-light: #60a5fa;
  --info-dark: #2563eb;

  /* Enhanced Neutrals */
  --bg-primary: #0f0f23;
  --bg-secondary: #1a1a2e;
  --bg-tertiary: #16213e;
  --bg-quaternary: #0e3460;
  --bg-surface: #1e1e3f;

  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;
  --text-muted: #64748b;
  --text-disabled: #475569;

  /* Enhanced Gradients */
  --gradient-primary: linear-gradient(135deg, #8b5cf6 0%, #06b6d4 50%, #f59e0b 100%);
  --gradient-secondary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-accent: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-warm: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  --gradient-cool: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  --gradient-dark: linear-gradient(135deg, #232526 0%, #414345 100%);
  --gradient-mesh: radial-gradient(at 40% 20%, #8b5cf6 0px, transparent 50%),
                   radial-gradient(at 80% 0%, #06b6d4 0px, transparent 50%),
                   radial-gradient(at 0% 50%, #f59e0b 0px, transparent 50%),
                   radial-gradient(at 80% 50%, #ef4444 0px, transparent 50%),
                   radial-gradient(at 0% 100%, #22c55e 0px, transparent 50%),
                   radial-gradient(at 80% 100%, #8b5cf6 0px, transparent 50%),
                   radial-gradient(at 0% 0%, #06b6d4 0px, transparent 50%);

  /* Glass Effects */
  --glass-bg: rgba(248, 250, 252, 0.03);
  --glass-bg-strong: rgba(248, 250, 252, 0.08);
  --glass-bg-stronger: rgba(248, 250, 252, 0.12);
  --glass-border: rgba(248, 250, 252, 0.08);
  --glass-border-strong: rgba(248, 250, 252, 0.15);

  /* Enhanced Borders */
  --border-light: rgba(248, 250, 252, 0.06);
  --border-medium: rgba(248, 250, 252, 0.12);
  --border-strong: rgba(248, 250, 252, 0.18);
  --border-accent: rgba(139, 92, 246, 0.3);

  /* Enhanced Shadows */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --shadow-glow: 0 0 20px rgba(139, 92, 246, 0.3);
  --shadow-glow-lg: 0 0 40px rgba(139, 92, 246, 0.4);

  /* Spacing Scale */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;

  /* Border Radius Scale */
  --radius-xs: 0.125rem;
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;

  /* Typography Scale */
  --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;

  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;

  /* Animation Durations */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;
  --duration-slower: 500ms;

  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Enhanced Glass Effects */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--glass-border);
  position: relative;
  overflow: hidden;
}

.glass::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--glass-border-strong), transparent);
}

.glass-strong {
  background: var(--glass-bg-strong);
  backdrop-filter: blur(24px);
  -webkit-backdrop-filter: blur(24px);
  border-radius: var(--radius-3xl);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--glass-border-strong);
  position: relative;
  overflow: hidden;
}

.glass-strong::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(248, 250, 252, 0.3), transparent);
}

.glass-stronger {
  background: var(--glass-bg-stronger);
  backdrop-filter: blur(32px);
  -webkit-backdrop-filter: blur(32px);
  border-radius: var(--radius-3xl);
  box-shadow: var(--shadow-2xl);
  border: 1px solid var(--border-strong);
}

.elevate {
  box-shadow: var(--shadow-2xl);
  transform: translateY(-2px);
}

.elevate-hover {
  transition: all var(--duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
}

.elevate-hover:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl);
}

/* Enhanced Text Gradients */
.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  font-weight: 700;
}

.text-gradient-secondary {
  background: var(--gradient-secondary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  font-weight: 600;
}

.text-gradient-accent {
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  font-weight: 600;
}

.text-glow {
  text-shadow: 0 0 20px rgba(139, 92, 246, 0.5);
}

/* Enhanced Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-xl);
  font-family: var(--font-family-sans);
  font-weight: 600;
  font-size: var(--font-size-sm);
  border: none;
  cursor: pointer;
  transition: all var(--duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  user-select: none;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--duration-slow) ease;
}

.btn:hover::before {
  left: 100%;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-secondary {
  background: var(--glass-bg-strong);
  color: var(--text-primary);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(16px);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--glass-bg-stronger);
  border-color: var(--glass-border-strong);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-accent {
  background: var(--gradient-accent);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-accent:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 0 20px rgba(245, 158, 11, 0.4);
}

.btn-success {
  background: var(--gradient-success);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-success:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.4);
}

.btn-danger {
  background: linear-gradient(135deg, var(--error) 0%, var(--error-dark) 100%);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-danger:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.4);
}

.btn-ghost {
  background: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border-light);
}

.btn-ghost:hover:not(:disabled) {
  background: var(--glass-bg);
  color: var(--text-primary);
  border-color: var(--border-medium);
}

/* Button Sizes */
.btn-sm {
  padding: var(--space-xs) var(--space-md);
  font-size: var(--font-size-xs);
  border-radius: var(--radius-lg);
}

.btn-lg {
  padding: var(--space-md) var(--space-xl);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-2xl);
}

.btn-xl {
  padding: var(--space-lg) var(--space-2xl);
  font-size: var(--font-size-xl);
  border-radius: var(--radius-2xl);
}

/* Enhanced Inputs */
.input {
  width: 100%;
  padding: var(--space-sm) var(--space-md);
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  color: var(--text-primary);
  font-family: var(--font-family-sans);
  font-size: var(--font-size-sm);
  font-weight: 400;
  transition: all var(--duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(16px);
  position: relative;
}

.input:focus {
  outline: none;
  border-color: var(--primary);
  background: var(--glass-bg-strong);
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1), var(--shadow-lg);
  transform: translateY(-1px);
}

.input::placeholder {
  color: var(--text-tertiary);
  font-weight: 400;
}

.input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.input-error {
  border-color: var(--error);
  background: rgba(239, 68, 68, 0.05);
}

.input-error:focus {
  border-color: var(--error);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.input-success {
  border-color: var(--success);
  background: rgba(16, 185, 129, 0.05);
}

.textarea {
  resize: vertical;
  min-height: 120px;
  font-family: var(--font-family-mono);
  line-height: 1.6;
}

/* Enhanced Utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-mono { font-family: var(--font-family-mono); }
.font-sans { font-family: var(--font-family-sans); }

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Enhanced Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-fade-in {
  animation: fadeIn var(--duration-slow) cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slide-in-right {
  animation: slideInFromRight var(--duration-slow) cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slide-in-left {
  animation: slideInFromLeft var(--duration-slow) cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-scale-in {
  animation: scaleIn var(--duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Loading States */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 1.5s infinite;
}

.skeleton {
  background: var(--glass-bg);
  border-radius: var(--radius-lg);
  position: relative;
  overflow: hidden;
}

.skeleton::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 1.5s infinite;
}

/* Card Components */
.card {
  background: var(--glass-bg);
  backdrop-filter: blur(16px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  padding: var(--space-lg);
  box-shadow: var(--shadow-lg);
  transition: all var(--duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  border-color: var(--glass-border-strong);
}

.card-header {
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-md);
  border-bottom: 1px solid var(--border-light);
}

.card-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.card-description {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
  :root {
    --space-xs: 0.2rem;
    --space-sm: 0.4rem;
    --space-md: 0.8rem;
    --space-lg: 1.2rem;
    --space-xl: 1.6rem;
    --space-2xl: 2.4rem;
    --space-3xl: 3.2rem;
  }
}

@media (max-width: 768px) {
  :root {
    --font-size-xs: 0.7rem;
    --font-size-sm: 0.8rem;
    --font-size-base: 0.9rem;
    --font-size-lg: 1rem;
    --font-size-xl: 1.1rem;
    --font-size-2xl: 1.3rem;
    --font-size-3xl: 1.6rem;
    --font-size-4xl: 2rem;
    --font-size-5xl: 2.5rem;
  }

  .btn {
    padding: var(--space-xs) var(--space-md);
    font-size: var(--font-size-xs);
  }

  .btn-lg {
    padding: var(--space-sm) var(--space-lg);
    font-size: var(--font-size-sm);
  }

  .input {
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--font-size-xs);
  }

  .glass, .glass-strong {
    border-radius: var(--radius-xl);
  }

  .card {
    padding: var(--space-md);
  }
}

@media (max-width: 480px) {
  :root {
    --space-xs: 0.15rem;
    --space-sm: 0.3rem;
    --space-md: 0.6rem;
    --space-lg: 1rem;
    --space-xl: 1.4rem;
    --space-2xl: 2rem;
    --space-3xl: 2.8rem;
  }

  .glass, .glass-strong {
    border-radius: var(--radius-lg);
    padding: var(--space-sm);
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  :root {
    --glass-bg: rgba(248, 250, 252, 0.02);
    --glass-bg-strong: rgba(248, 250, 252, 0.06);
    --glass-bg-stronger: rgba(248, 250, 252, 0.1);
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  :root {
    --glass-border: rgba(248, 250, 252, 0.3);
    --glass-border-strong: rgba(248, 250, 252, 0.5);
    --border-light: rgba(248, 250, 252, 0.2);
    --border-medium: rgba(248, 250, 252, 0.4);
    --border-strong: rgba(248, 250, 252, 0.6);
  }
}


