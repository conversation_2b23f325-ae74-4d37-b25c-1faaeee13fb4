import { useState } from "react";
import api from "../api";
import AiActions from "./AiActions";

export default function NoteCard({ note, refresh }) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  const deleteNote = async () => {
    if (isDeleting) return;

    setIsDeleting(true);
    try {
      await api.delete(`/notes/${note._id}`);
      refresh();
    } catch (error) {
      console.error("Error deleting note:", error);
      setIsDeleting(false);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const truncateContent = (content, maxLength = 150) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  return (
    <div className="group glass elevate-hover rounded-3xl overflow-hidden transition-all duration-300 hover:shadow-glow border border-glass-border hover:border-glass-border-strong">
      {/* Card Header */}
      <div className="p-6 pb-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1 min-w-0">
            <h3 className="text-xl font-bold text-gradient mb-1 truncate">
              {note.title || "Untitled Note"}
            </h3>
            <div className="flex items-center gap-2 text-text-tertiary text-sm">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              {formatDate(note.createdAt || Date.now())}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-2 text-text-tertiary hover:text-text-primary transition-colors rounded-lg hover:bg-glass-bg"
              title={isExpanded ? "Collapse" : "Expand"}
            >
              <svg className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="mb-4">
          <p className={`text-text-secondary leading-relaxed ${isExpanded ? '' : 'line-clamp-3'}`}>
            {isExpanded ? note.content : truncateContent(note.content)}
          </p>
          {note.content.length > 150 && !isExpanded && (
            <button
              onClick={() => setIsExpanded(true)}
              className="text-primary hover:text-primary-light text-sm font-medium mt-2 transition-colors"
            >
              Read more
            </button>
          )}
        </div>

        {/* Tags or Categories (if available) */}
        {note.tags && note.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-4">
            {note.tags.map((tag, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-primary/10 text-primary text-xs font-medium rounded-full border border-primary/20"
              >
                {tag}
              </span>
            ))}
          </div>
        )}
      </div>

      {/* Card Footer */}
      <div className="px-6 pb-6">
        <div className="flex items-center justify-between gap-3">
          {/* AI Actions */}
          <div className="flex-1">
            <AiActions note={note} />
          </div>

          {/* Delete Button */}
          <button
            onClick={deleteNote}
            disabled={isDeleting}
            className="btn btn-danger btn-sm group/delete relative overflow-hidden"
            title="Delete note"
          >
            <span className="flex items-center gap-2 relative z-10">
              {isDeleting ? (
                <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              ) : (
                <svg className="w-4 h-4 transition-transform group-hover/delete:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              )}
              <span className="hidden sm:inline">
                {isDeleting ? "Deleting..." : "Delete"}
              </span>
            </span>
          </button>
        </div>
      </div>

      {/* Hover Glow Effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none rounded-3xl"></div>
    </div>
  );
}
