import { useNavigate } from "react-router-dom";


export default function Navbar() {
  const navigate = useNavigate();

  const handleLogout = () => {
    localStorage.removeItem("token");
    navigate("/login");
  };

  return (
    <nav className="sticky top-0 z-40 glass-strong elevate flex justify-between items-center px-8 py-4 mb-6 shadow-lg backdrop-blur-lg">
      <div className="flex items-center gap-3 cursor-pointer" onClick={() => navigate("/dashboard")}> 
        <div className="w-10 h-10 bg-gradient-to-br from-primary to-accent rounded-xl flex items-center justify-center">
          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <span className="text-2xl font-extrabold text-gradient tracking-wide">Smart Notes</span>
      </div>
      <button
        onClick={handleLogout}
        className="btn px-5 py-2 bg-gradient-to-r from-red-500 to-pink-500 text-white font-semibold rounded-xl shadow hover:scale-105 transition-transform"
      >
        Logout
      </button>
    </nav>
  );
}
