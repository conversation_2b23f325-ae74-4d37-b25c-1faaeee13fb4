import { useEffect, useState } from "react";
import axios from "axios";
import Navbar from "../components/Navbar.jsx";
import NoteCard from "../components/NoteCard.jsx";

export default function Dashboard() {
  const [notes, setNotes] = useState([]);
  const [newNote, setNewNote] = useState("");
  const [newNoteTitle, setNewNoteTitle] = useState("");
  const [aiResult, setAiResult] = useState("");
  const [loading, setLoading] = useState(true);
  const [addingNote, setAddingNote] = useState(false);
  const token = localStorage.getItem("token");

  useEffect(() => {
    fetchNotes();
  }, []);

  const fetchNotes = async () => {
    try {
      setLoading(true);
      const res = await axios.get("http://localhost:5000/api/notes", {
        headers: { Authorization: `Bearer ${token}` },
      });
      setNotes(res.data);
    } catch (err) {
      console.error(err.response?.data?.message || "Error fetching notes");
    } finally {
      setLoading(false);
    }
  };

  const addNote = async () => {
    if (!newNote.trim()) return;
    
    try {
      setAddingNote(true);
      await axios.post(
        "http://localhost:5000/api/notes",
        { 
          content: newNote 
        },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      setNewNote("");
      setNewNoteTitle("");
      fetchNotes();
    } catch (err) {
      console.error(err.response?.data?.message || "Error adding note");
    } finally {
      setAddingNote(false);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      addNote();
    }
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 right-20 w-72 h-72 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-tr from-accent/10 to-primary/10 rounded-full blur-3xl animate-pulse" style={{animationDelay: '3s'}}></div>
      </div>

      <Navbar />

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header Section */}
        <div className="text-center mb-12 animate-fade-in">
          <h1 className="text-5xl font-extrabold text-gradient mb-4 tracking-tight">
            Smart Notes Dashboard
          </h1>
          <p className="text-text-secondary text-xl font-medium max-w-2xl mx-auto">
            Organize your thoughts, enhance with AI, and boost your productivity
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 animate-slide-in-left">
          <div className="glass p-6 rounded-2xl text-center elevate-hover">
            <div className="w-12 h-12 bg-gradient-to-br from-primary to-secondary rounded-xl mx-auto mb-3 flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="text-2xl font-bold text-primary mb-1">{notes.length}</h3>
            <p className="text-text-secondary text-sm font-medium">Total Notes</p>
          </div>
          
          <div className="glass p-6 rounded-2xl text-center elevate-hover">
            <div className="w-12 h-12 bg-gradient-to-br from-accent to-warning rounded-xl mx-auto mb-3 flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h3 className="text-2xl font-bold text-accent mb-1">AI</h3>
            <p className="text-text-secondary text-sm font-medium">Powered</p>
          </div>
          
          <div className="glass p-6 rounded-2xl text-center elevate-hover">
            <div className="w-12 h-12 bg-gradient-to-br from-success to-info rounded-xl mx-auto mb-3 flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h3 className="text-2xl font-bold text-success mb-1">100%</h3>
            <p className="text-text-secondary text-sm font-medium">Organized</p>
          </div>
        </div>

        {/* Add New Note Section */}
        <div className="glass-strong p-8 rounded-3xl mb-8 animate-scale-in" style={{animationDelay: '0.2s'}}>
          <div className="flex items-center gap-3 mb-6">
            <div className="w-10 h-10 bg-gradient-to-br from-primary to-secondary rounded-xl flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-gradient">Create New Note</h2>
          </div>
          
          <div className="space-y-4">
            <div className="relative">
              <textarea
                placeholder="Write your note here... (Ctrl+Enter to save)"
                value={newNote}
                onChange={(e) => setNewNote(e.target.value)}
                className="input textarea w-full resize-none"
                rows={4}
                onKeyDown={handleKeyDown}
              />
              <div className="absolute bottom-3 right-3 text-text-tertiary text-xs">
                Ctrl+Enter to save
              </div>
            </div>
            
            <button
              onClick={addNote}
              disabled={!newNote.trim() || addingNote}
              className="btn btn-primary btn-lg group"
            >
              <span className="flex items-center gap-2">
                {addingNote ? (
                  <svg className="w-5 h-5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5 transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                )}
                {addingNote ? "Adding Note..." : "Add Note"}
              </span>
            </button>
          </div>
        </div>

        {/* Notes Grid */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-3xl font-bold text-gradient">Your Notes</h2>
            {notes.length > 0 && (
              <div className="text-text-secondary text-sm font-medium">
                {notes.length} {notes.length === 1 ? 'note' : 'notes'}
              </div>
            )}
          </div>
          
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="skeleton h-48 animate-pulse"></div>
              ))}
            </div>
          ) : notes.length === 0 ? (
            <div className="text-center py-16 animate-fade-in">
              <div className="w-24 h-24 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-3xl mx-auto mb-6 flex items-center justify-center">
                <svg className="w-12 h-12 text-text-tertiary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-text-secondary mb-2">No notes yet</h3>
              <p className="text-text-tertiary max-w-md mx-auto">
                Start by creating your first note above. You can enhance it with AI features once it's created.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {notes.map((note, index) => (
                <div 
                  key={note._id} 
                  className="animate-fade-in"
                  style={{animationDelay: `${index * 0.1}s`}}
                >
                  <NoteCard note={note} refresh={fetchNotes} />
                </div>
              ))}
            </div>
          )}
        </div>

        {/* AI Result Display */}
        {aiResult && (
          <div className="glass-strong p-8 rounded-3xl animate-scale-in">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-gradient-to-br from-accent to-warning rounded-xl flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-gradient">AI Result</h2>
              <button
                onClick={() => setAiResult("")}
                className="ml-auto btn btn-ghost btn-sm"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="glass p-4 rounded-xl">
              <pre className="whitespace-pre-wrap text-text-primary font-mono text-sm leading-relaxed">
                {aiResult}
              </pre>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
